/* Status Cards Component Styles */
.status-card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s ease-in-out;
}

.status-card:hover {
    transform: translateY(-2px);
}

/* Status indicators */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-indicator.running {
    background-color: #28a745;
    animation: pulse 2s infinite;
}

.status-indicator.stopped {
    background-color: #6c757d;
}
