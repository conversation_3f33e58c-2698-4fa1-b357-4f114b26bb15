/* Responsive Design Styles */
@media (max-width: 768px) {
    .status-card {
        margin-bottom: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        font-size: 0.9rem;
    }

    .post-header {
        padding: 0.75rem;
    }

    .post-content {
        padding: 0 0.75rem 0.75rem 0.75rem;
    }

    .comments-container {
        max-height: 300px;
    }

    .comment-item {
        padding: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .confidence-bar {
        height: 3px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #fff;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #fff;
    }
    
    .card-header {
        background-color: #2d2d2d;
        border-bottom-color: #404040;
    }
    
    .comment-item {
        background-color: #404040;
    }
    
    .comment-item.spam {
        background-color: #4a2c2c;
    }
    
    .comment-item.normal {
        background-color: #2c4a2c;
    }
}
