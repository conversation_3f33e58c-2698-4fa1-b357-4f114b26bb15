<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Component Test Page</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/css/dashboard.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Component Test Page</h1>
        <div class="alert alert-info">
            <h4>Testing Refactored Components</h4>
            <p>This page tests if all JavaScript modules load correctly.</p>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Module Loading Test</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-results">
                            <p>Testing modules...</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>UI Test</h5>
                    </div>
                    <div class="card-body">
                        <button id="test-toast" class="btn btn-primary">Test Toast</button>
                        <button id="test-loading" class="btn btn-secondary">Test Loading</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Load modular JavaScript components -->
    <script src="/js/modules/UIHelpers.js"></script>
    <script src="/js/modules/StatusManager.js"></script>
    <script src="/js/modules/SpamDetectionTester.js"></script>
    <script src="/js/modules/RealTimeManager.js"></script>
    <script src="/js/modules/PostManager.js"></script>
    <script src="/js/modules/CommentManager.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const testResults = document.getElementById('test-results');
            let results = [];

            // Test module loading
            const modules = [
                'UIHelpers',
                'StatusManager', 
                'SpamDetectionTester',
                'RealTimeManager',
                'PostManager',
                'CommentManager'
            ];

            modules.forEach(moduleName => {
                if (typeof window[moduleName] !== 'undefined') {
                    results.push(`✅ ${moduleName} loaded successfully`);
                } else {
                    results.push(`❌ ${moduleName} failed to load`);
                }
            });

            testResults.innerHTML = results.map(result => `<p>${result}</p>`).join('');

            // Test UI functionality
            const uiHelpers = new UIHelpers();
            
            document.getElementById('test-toast').addEventListener('click', () => {
                uiHelpers.showToast('Test toast message!', 'success');
            });

            document.getElementById('test-loading').addEventListener('click', () => {
                uiHelpers.showLoading('test-loading');
                setTimeout(() => {
                    uiHelpers.hideLoading('test-loading');
                }, 2000);
            });
        });
    </script>
</body>
</html>
