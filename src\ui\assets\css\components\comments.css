/* Comments Component Styles */
.comment-item {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    border-left: 3px solid #6c757d;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
    position: relative;
}

.comment-item:hover {
    transform: translateX(2px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

.comment-item:last-child {
    margin-bottom: 0;
}

.comment-item.spam {
    border-left-color: #dc3545;
    background-color: #fff5f5;
}

.comment-item.normal {
    border-left-color: #28a745;
    background-color: #f8fff8;
}

.prediction-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.confidence-bar {
    height: 4px;
    background-color: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.confidence-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.confidence-fill.high {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.confidence-fill.medium {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.confidence-fill.low {
    background: linear-gradient(90deg, #6c757d, #adb5bd);
}

/* New comment animations and styling */
.comment-item.new-comment {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border-left: 4px solid #28a745;
    animation: newCommentPulse 2s ease-in-out;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.comment-item.new-comment::before {
    content: "NEW";
    position: absolute;
    top: 8px;
    right: 8px;
    background: #28a745;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
    animation: newBadgeFade 5s ease-in-out forwards;
}

@keyframes newCommentPulse {
    0% {
        background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
        transform: scale(1);
    }
    50% {
        background: linear-gradient(135deg, #d4edda 0%, #e8f5e8 100%);
        transform: scale(1.02);
    }
    100% {
        background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
        transform: scale(1);
    }
}

@keyframes newBadgeFade {
    0% { opacity: 1; }
    80% { opacity: 1; }
    100% { opacity: 0; }
}

/* Comment edit and delete animations */
.comment-item.comment-edited {
    border-left: 4px solid #17a2b8;
    background: linear-gradient(135deg, #e8f4f8 0%, #f0f9fa 100%);
}

.edited-comment {
    position: relative;
}

.edited-indicator {
    display: block;
    margin-top: 5px;
    font-style: italic;
    color: #17a2b8 !important;
}

.edited-time {
    margin-left: 5px;
}

@keyframes editPulse {
    0% {
        background: linear-gradient(135deg, #e8f4f8 0%, #f0f9fa 100%);
        transform: scale(1);
    }
    50% {
        background: linear-gradient(135deg, #bee5eb 0%, #d1ecf1 100%);
        transform: scale(1.01);
    }
    100% {
        background: linear-gradient(135deg, #e8f4f8 0%, #f0f9fa 100%);
        transform: scale(1);
    }
}

@keyframes fadeOutSlide {
    0% {
        opacity: 1;
        transform: translateX(0);
        max-height: 200px;
    }
    50% {
        opacity: 0.5;
        transform: translateX(-20px);
    }
    100% {
        opacity: 0;
        transform: translateX(-50px);
        max-height: 0;
        padding: 0;
        margin: 0;
    }
}

/* Deleted comment placeholder */
.comment-deleted-placeholder {
    background: #f8d7da;
    border-left: 4px solid #dc3545;
    padding: 10px;
    margin: 5px 0;
    border-radius: 4px;
    font-style: italic;
    color: #721c24;
    animation: fadeIn 0.3s ease-in;
}

/* Spam removal animation */
.comment-item.spam-being-removed {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-left: 4px solid #dc3545;
}

@keyframes spamRemovalSlide {
    0% {
        opacity: 1;
        transform: translateX(0) scale(1);
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    }
    25% {
        transform: translateX(-10px) scale(0.98);
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
    }
    50% {
        transform: translateX(10px) scale(0.95);
        opacity: 0.8;
    }
    75% {
        transform: translateX(-5px) scale(0.9);
        opacity: 0.5;
    }
    100% {
        opacity: 0;
        transform: translateX(-100px) scale(0.8);
        max-height: 0;
        padding: 0;
        margin: 0;
        border: none;
    }
}

/* Moderation removal animation */
.comment-item.moderation-deleted {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-left: 4px solid #ffc107;
}

@keyframes moderationRemoval {
    0% {
        opacity: 1;
        transform: translateX(0) scale(1);
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border-left: 4px solid #ffc107;
    }
    20% {
        background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);
        color: #212529;
        border-left: 4px solid #ff8c00;
    }
    40% {
        transform: translateX(-15px) scale(0.98);
        background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 100%);
        color: white;
    }
    60% {
        transform: translateX(15px) scale(0.95);
        opacity: 0.8;
    }
    80% {
        transform: translateX(-8px) scale(0.9);
        opacity: 0.4;
    }
    100% {
        opacity: 0;
        transform: translateX(-120px) scale(0.7);
        max-height: 0;
        padding: 0;
        margin: 0;
        border: none;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateX(0);
        max-height: 200px;
    }
    to {
        opacity: 0;
        transform: translateX(-20px);
        max-height: 0;
        padding: 0;
        margin: 0;
    }
}
