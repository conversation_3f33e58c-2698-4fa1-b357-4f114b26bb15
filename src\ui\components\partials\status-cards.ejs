<!-- Status Cards Component -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card status-card">
            <div class="card-body text-center">
                <i class="fas fa-play-circle fa-2x text-success mb-2"></i>
                <h6>Monitor Status</h6>
                <span id="monitor-status" class="badge bg-<%= monitorStatus.isRunning ? 'success' : 'secondary' %>">
                    <%= monitorStatus.isRunning ? 'Running' : 'Stopped' %>
                </span>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card status-card">
            <div class="card-body text-center">
                <i class="fas fa-comments fa-2x text-info mb-2"></i>
                <h6>Comments Processed</h6>
                <h4 id="comments-processed"><%= monitorStatus.commentsProcessed %></h4>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card status-card">
            <div class="card-body text-center">
                <i class="fas fa-trash fa-2x text-danger mb-2"></i>
                <h6>Spam Removed</h6>
                <h4 id="spam-removed"><%= monitorStatus.spamRemoved %></h4>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card status-card">
            <div class="card-body text-center">
                <i class="fas-facebook fa-2x text-primary mb-2"></i>
                <h6>Facebook Page</h6>
                <small class="text-muted"><%= pageId || 'Not configured' %></small>
            </div>
        </div>
    </div>
</div>
