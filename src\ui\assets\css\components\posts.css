/* Posts Component Styles */
.post-item {
    border-left: 4px solid #007bff;
    background-color: #fff;
    margin-bottom: 1rem;
    border-radius: 0 8px 8px 0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.post-item:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.post-item.expanded {
    border-left-color: #28a745;
}

.post-item.comments-loaded {
    border-left-color: #007bff;
    border-left-width: 4px;
}

.post-item.comments-loaded .post-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.post-header {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.post-header:hover {
    background-color: #f8f9fa;
}

.post-content {
    padding: 0 1rem 1rem 1rem;
}

.expand-icon {
    transition: transform 0.3s ease;
}

.expand-icon.expanded {
    transform: rotate(180deg);
}

.comments-section {
    display: none;
    animation: slideDown 0.3s ease-out;
    border-top: 1px solid #e9ecef;
    background-color: #f8f9fa;
    margin: 0 -1rem -1rem -1rem;
    border-radius: 0 0 8px 8px;
}

.comments-section.show {
    display: block;
}

.comments-section.preloaded {
    background-color: #f0f8ff;
    border-top-color: #007bff;
}

.comments-section.preloaded .comments-header {
    background-color: #e3f2fd;
    border-bottom: 1px solid #bbdefb;
}

.comments-header {
    padding: 1rem;
    background-color: #e9ecef;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.comments-container {
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 500px;
    }
}
